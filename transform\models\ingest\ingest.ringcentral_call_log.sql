{{ config(
    alias = 'ringcentral_call_log'
) }}

with source_data as (
    select
        -- Core identification
        session_id,
        call_record_id,
        telephony_session_id,

        -- Timing information
        start_time,
        duration,
        created_timestamp,

        -- Participant information
        from_emp_upn,
        from_entra_id,
        from_phone_num,
        to_emp_upn,
        to_entra_id,
        to_phone_num,

        -- Call details
        direction,
        call_type,
        action,
        result,

        -- Additional fields from actual table
        recording_id,
        from_ext,
        to_ext,

        -- Metadata
        ringcentral_json

    from {{ source('ringcentral', 'call_log') }}

    -- Data quality filters
    where session_id is not null
      and start_time is not null
      and direction in ('I', 'O')  -- Only valid directions
      and duration >= 0  -- No negative durations
),

data_quality_checks as (
    select *,
        -- Add data quality flags
        case
            when session_id is null then 'Missing Session ID'
            when start_time is null then 'Missing Start Time'
            when direction not in ('I', 'O') then 'Invalid Direction'
            when duration < 0 then 'Negative Duration'
            else 'Valid'
        end as data_quality_status,

        -- Add row number for duplicate detection
        row_number() over (
            partition by session_id
            order by created_timestamp desc
        ) as row_num

    from source_data
),

final as (
    select
        session_id,
        call_record_id,
        telephony_session_id,
        start_time,
        duration,
        created_timestamp,
        from_emp_upn,
        from_entra_id,
        from_phone_num,
        to_emp_upn,
        to_entra_id,
        to_phone_num,
        direction,
        call_type,
        action,
        result,
        recording_id,
        from_ext,
        to_ext,
        ringcentral_json,
        data_quality_status,

        -- Add processing metadata
        current_timestamp() as processed_at

    from data_quality_checks
    where row_num = 1  -- Deduplicate by session_id, keeping most recent
      and data_quality_status = 'Valid'
)

select * from final
