{{ config(
    alias = 'dim_call_outcome'
) }}

with call_outcomes as (
    select distinct
        result,
        call_outcome_category,
        interaction_type
    from {{ ref('ods.ringcentral_call_log') }}
),

dimension_table as (
    select
        hash(result) as call_outcome_key,
        result as call_result,
        call_outcome_category,
        interaction_type,

        -- Success indicators
        case when call_outcome_category = 'Successful' then 1 else 0 end as is_successful,
        case when call_outcome_category = 'Voicemail' then 1 else 0 end as is_voicemail,
        case when call_outcome_category = 'No Answer' then 1 else 0 end as is_no_answer,
        case when call_outcome_category = 'Data Quality Issue' then 1 else 0 end as is_data_quality_issue,
        case when call_outcome_category = 'Failed' then 1 else 0 end as is_failed,

        -- Interaction type indicators
        case when interaction_type = 'Meaningful' then 1 else 0 end as is_meaningful_interaction,
        case when interaction_type = 'Brief' then 1 else 0 end as is_brief_interaction,
        case when interaction_type = 'No Connection' then 1 else 0 end as is_no_connection,

        -- Metadata
        current_timestamp() as created_at,
        current_timestamp() as updated_at

    from call_outcomes
)

select * from dimension_table
