version: 2

models:
  - name: udw.fact_call_activity
    description: "Call activity fact table containing one record per phone call with measures and dimensional references"
    config:
      alias: fact_call_activity
    columns:
      - name: call_activity_key
        description: "Unique surrogate key for each call activity record"
        tests:
          - unique:
              name: udw_fact_call_activity_key_unique
          - not_null:
              name: udw_fact_call_activity_key_not_null
      - name: session_id
        description: "Original session ID from RingCentral (degenerate dimension)"
        tests:
          - not_null:
              name: udw_fact_call_activity_session_id_not_null
      - name: from_employee_key
        description: "Foreign key to employee dimension for calling employee"
        tests:
          - not_null:
              name: udw_fact_call_activity_from_employee_key_not_null
      - name: to_employee_key
        description: "Foreign key to employee dimension for receiving employee"
        tests:
          - not_null:
              name: udw_fact_call_activity_to_employee_key_not_null
      - name: call_outcome_key
        description: "Foreign key to call outcome dimension"
        tests:
          - not_null:
              name: udw_fact_call_activity_call_outcome_key_not_null
          - relationships:
              to: ref('udw.dim_call_outcome')
              field: call_outcome_key
              name: udw_fact_call_activity_call_outcome_key_ref
      - name: call_date_key
        description: "Date key in YYYYMMDD format"
        tests:
          - not_null:
              name: udw_fact_call_activity_call_date_key_not_null
      - name: duration_seconds
        description: "Call duration in seconds (measure)"
        tests:
          - not_null:
              name: udw_fact_call_activity_duration_not_null
      - name: call_count
        description: "Call count measure (always 1 per record)"
        tests:
          - not_null:
              name: udw_fact_call_activity_call_count_not_null
          - accepted_values:
              values: [1]
              name: udw_fact_call_activity_call_count_valid
      - name: is_connected_flag
        description: "Flag indicating if call was connected (1/0)"
        tests:
          - accepted_values:
              values: [0, 1]
              name: udw_fact_call_activity_connected_flag_valid
      - name: is_meaningful_interaction_flag
        description: "Flag indicating if interaction was meaningful (1/0)"
        tests:
          - accepted_values:
              values: [0, 1]
              name: udw_fact_call_activity_meaningful_flag_valid
      - name: is_internal_call_flag
        description: "Flag indicating if call was internal (1/0)"
        tests:
          - accepted_values:
              values: [0, 1]
              name: udw_fact_call_activity_internal_flag_valid
      - name: direction_code
        description: "Call direction code (I/O)"
        tests:
          - accepted_values:
              values: ['I', 'O']
              name: udw_fact_call_activity_direction_valid
      - name: call_classification
        description: "Business classification of the call"
        tests:
          - accepted_values:
              values: ['Internal', 'Employee Outbound', 'Employee Inbound', 'External']
              name: udw_fact_call_activity_classification_valid
