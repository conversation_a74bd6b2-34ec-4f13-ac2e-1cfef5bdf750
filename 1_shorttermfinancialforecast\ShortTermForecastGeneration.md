# Short-Term Forecast Generation (Technical)

This document provides a technical deep dive into the short-term financial forecast generation process, referencing the following models:

- `prep.billing_unified_forecast.sql`
- `udw.fact_billing_forecast.sql`
- `udw.fact_billing_unified.sql`

---

## Overview

The pipeline projects future billing, cost, and spread for contract placements using recent historical data and adaptive business logic. The process consists of:

1. **Preparation & Feature Engineering** (`prep.billing_unified_forecast.sql`)
2. **Forecast Record Generation** (`udw.fact_billing_forecast.sql`)
3. **Unification with Actuals** (`udw.fact_billing_unified.sql`)

---

## 1. Preparation & Feature Engineering (`prep.billing_unified_forecast.sql`)

### Data Quality Flags & Filtering

Outlier and data quality flags are set for each billing record:
```sql
case when bill_hours < 0 then true else false end as is_negative_hours,
case when bill_rate_amount < 0 then true else false end as is_negative_bill_rate,
case when cost_rate_amount < 0 then true else false end as is_negative_cost_rate,
case when bill_rate_amount >= 5000 then true else false end as is_high_bill_rate_outlier,
case when cost_rate_amount >= 1000 then true else false end as is_high_cost_rate_outlier,
case when bill_hours >= 80 then true else false end as is_high_hours_outlier
```

Records are filtered for downstream calculations:
```sql
where not is_negative_hours
  and not is_negative_bill_rate
  and not is_negative_cost_rate
  and not is_high_bill_rate_outlier
  and not is_high_cost_rate_outlier
  and not is_high_hours_outlier
  and not (bill_hours = 0 and spread_amount != 0)
```

### Feature Engineering

- **Aggregates per placement:**
```sql
PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY bill_hours) as median_weekly_hours,
PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY bill_amount / NULLIF(bill_hours, 0)) as median_bill_rate,
PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY cost_amount / NULLIF(bill_hours, 0)) as median_cost_rate,
max(billing_date) as last_billing_date,
count(*) as billing_record_count,
avg(bill_hours) as avg_weekly_hours,
stddev(bill_hours) as stddev_weekly_hours,
stddev(bill_amount / NULLIF(bill_hours, 0)) as bill_rate_stddev,
stddev(cost_amount / NULLIF(bill_hours, 0)) as cost_rate_stddev
```

- **Recent trends:**
```sql
greatest(-2.0, least(2.0, avg(case when prev_week_spread != 0 then (spread_amount - prev_week_spread) / nullif(prev_week_spread, 0) else 0 end))) as recent_spread_trend
```

- **Recent weekly patterns:**
```sql
avg(weekly_hours) as recent_avg_weekly_hours,
median(weekly_hours) as recent_median_weekly_hours,
max(weekly_hours) as recent_max_weekly_hours,
count(*) as recent_weeks_count
```

### Forecast Eligibility Logic

A placement is eligible for forecasting if it passes all these checks:
```sql
case
  when pm.median_weekly_hours > 0
   and pm.median_bill_rate > 0
   and pm.last_billing_date >= dateadd('month', -6, {{ forecast_start_date_expr }})
   and (p.falloff_date is null or p.falloff_date > {{ forecast_start_date_expr }})
   and p.start_date <= {{ forecast_start_date_expr }}
   and pm.billing_record_count >= 1
   and pm.hours_variation_coefficient < 0.6
   and pm.bill_rate_stddev / nullif(pm.median_bill_rate, 0) < 0.4
  then true else false
end as is_forecast_eligible
```

### Forecast Confidence Score

The confidence score is a product of data sufficiency and stability:
```sql
case
  when pm.billing_record_count >= 12 then 1.0
  when pm.billing_record_count >= 8 then 0.8
  when pm.billing_record_count >= 4 then 0.6
  else 0.0
end
* (1 - pm.hours_variation_coefficient)
* (1 - (pm.bill_rate_stddev / nullif(pm.median_bill_rate, 0))) as forecast_confidence_score
```

### Trend & Conservatism Factors

These factors adjust forecasted rates based on the forecast start date:
```sql
case
  when {{ forecast_start_date_expr }} >= '2025-06-01' then 1.08
  when {{ forecast_start_date_expr }} >= '2025-03-01' then 1.05
  when {{ forecast_start_date_expr }} >= '2025-01-01' then 1.02
  else 1.0
end as spread_rate_trend_factor,

case
  when {{ forecast_start_date_expr }} >= '2025-06-01' then 0.88
  when {{ forecast_start_date_expr }} >= '2025-03-01' then 0.92
  when {{ forecast_start_date_expr }} >= '2025-01-01' then 0.95
  else 0.98
end as forecast_conservatism_factor
```

---

## 2. Forecast Record Generation (`udw.fact_billing_forecast.sql`)

### Hours Calculation Logic

The model uses a hierarchy of recent and historical patterns:
```sql
case
  when f.recent_weeks_count >= 3 and f.recent_median_weekly_hours >= 35 then least(40, f.recent_median_weekly_hours)
  when f.recent_weeks_count >= 3 then f.recent_median_weekly_hours
  when f.recent_weeks_count >= 2 then (f.recent_avg_weekly_hours * 0.7 + f.median_weekly_hours * 0.3)
  when f.latest_hours >= 35 then least(40, f.latest_hours)
  when f.latest_hours > 0 then f.latest_hours
  when f.median_weekly_hours >= 35 then least(40, f.median_weekly_hours)
  when f.avg_weekly_hours >= 35 then least(40, f.avg_weekly_hours)
  else greatest(0.1, f.median_weekly_hours * 0.3 + f.avg_weekly_hours * 0.7)
end as hours
```

### Rate Calculation Logic

Bill and cost rates are adaptively calculated with trend/conservatism factors:
```sql
case
  when f.recent_rate_records >= 5 then
    f.recent_median_bill_rate * f.spread_rate_trend_factor * f.forecast_conservatism_factor
  when f.recent_rate_records >= 3 then
    (f.recent_avg_bill_rate * 0.2 + f.median_bill_rate * 0.8) * f.spread_rate_trend_factor * f.forecast_conservatism_factor
  when f.latest_bill_rate > 0 then
    f.latest_bill_rate * f.spread_rate_trend_factor * f.forecast_conservatism_factor
  else
    f.median_bill_rate * f.spread_rate_trend_factor * f.forecast_conservatism_factor
end as calculated_bill_rate
```

Cost rate is calculated similarly, but with inverse factors:
```sql
case
  when f.recent_rate_records >= 5 then
    f.recent_median_cost_rate * (2.0 - f.spread_rate_trend_factor) * (2.0 - f.forecast_conservatism_factor)
  when f.recent_rate_records >= 3 then
    (f.recent_avg_cost_rate * 0.2 + f.median_cost_rate * 0.8) * (2.0 - f.spread_rate_trend_factor) * (2.0 - f.forecast_conservatism_factor)
  when f.latest_cost_rate > 0 then
    f.latest_cost_rate * (2.0 - f.spread_rate_trend_factor) * (2.0 - f.forecast_conservatism_factor)
  else
    f.median_cost_rate * (2.0 - f.spread_rate_trend_factor) * (2.0 - f.forecast_conservatism_factor)
end as calculated_cost_rate
```

### Spread Calculation

Spread is always calculated as:
```sql
(hours * calculated_bill_rate) - (hours * calculated_cost_rate) as spread_amount
```

### Series Indicator

Forecast records are labeled as 'Backtest' or 'Forecast' based on the date:
```sql
case
  when d.forecast_date < current_date() then 'Backtest'
  else 'Forecast'
end as series_indicator
```

---

## 3. Unification with Actuals (`udw.fact_billing_unified.sql`)

This model merges actual and forecasted records:

- **Actuals:**
  - Pulled from `prep.billing_unified`, tagged as `'Actual'`.
- **Forecasts:**
  - Pulled from `udw.fact_billing_forecast`, tagged as `'Backtest'` or `'Forecast'`.
  - Only future weeks (and all backtest) are included.

Example union logic:
```sql
select *, 'Actual' as series_indicator from {{ ref('prep.billing_unified') }}
union all
select *, series_indicator from {{ ref('udw.fact_billing_forecast') }}
where series_indicator = 'Backtest' or (series_indicator = 'Forecast' and forecast_date >= date_trunc('week', current_date()))
```

All records are then joined with employee and tenure dimensions and assigned unique keys.

---

## Key Technical Concepts

- **Forecast Eligibility:**
  - Strict SQL logic ensures only stable, active, and sufficiently recent placements are forecasted.
- **Confidence Score:**
  - Quantifies reliability using record count and stability metrics.
- **Trend/Conservatism Factors:**
  - Allow for business-driven adjustments to forecasted rates.
- **Backtesting:**
  - Enables validation of forecast logic by generating historical forecasts.
- **Series Indicator:**
  - Distinguishes actuals, backtests, and future forecasts for downstream analytics.

---

## Model Dependencies

```
prep.billing_unified_forecast.sql
   ↓
udw.fact_billing_forecast.sql
   ↓
udw.fact_billing_unified.sql
```

---

## Contact

For questions or enhancements, contact the data engineering team. 