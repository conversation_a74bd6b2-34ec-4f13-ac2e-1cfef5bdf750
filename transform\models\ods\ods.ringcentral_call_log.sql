{{ config(
    alias = 'ringcentral_call_log'
) }}

with ingest_data as (
    select * from {{ ref('ingest.ringcentral_call_log') }}
),

transformed as (
    select
        -- Core identification
        session_id,
        call_record_id,
        telephony_session_id,
        
        -- Time information with timezone conversion
        start_time as start_time_utc,
        convert_timezone('UTC', 'America/New_York', start_time) as start_time_local,
        duration,
        created_timestamp,
        
        -- Participant information
        from_emp_upn,
        from_entra_id,
        from_phone_num,
        to_emp_upn,
        to_entra_id,
        to_phone_num,
        
        -- Call details
        direction,
        call_type,
        action,
        result,
        
        -- Business logic transformations
        case 
            when duration >= 30 then 'Meaningful'
            when duration > 0 then 'Brief'
            else 'No Connection'
        end as interaction_type,
        
        case 
            when result in ('Call connected', 'Accepted') then 'Successful'
            when result in ('Missed', 'No Answer', 'Busy') then 'No Answer'
            when result = 'Voicemail' then 'Voicemail'
            when result = 'Wrong Number' then 'Data Quality Issue'
            else 'Failed'
        end as call_outcome_category,
        
        case 
            when from_emp_upn is not null and to_emp_upn is not null then 'Internal'
            when from_emp_upn is not null and direction = 'O' then 'Employee Outbound'
            when to_emp_upn is not null and direction = 'I' then 'Employee Inbound'
            else 'External'
        end as call_classification,
        
        -- Success flags
        case when result in ('Call connected', 'Accepted') then 1 else 0 end as is_connected_flag,
        case when duration >= 30 then 1 else 0 end as is_meaningful_interaction_flag,
        case when from_emp_upn is not null and to_emp_upn is not null then 1 else 0 end as is_internal_call_flag,
        
        -- Time-based analysis fields
        extract(hour from convert_timezone('UTC', 'America/New_York', start_time)) as call_hour,
        extract(dow from convert_timezone('UTC', 'America/New_York', start_time)) as call_day_of_week,
        date_trunc('week', convert_timezone('UTC', 'America/New_York', start_time)) as call_week,
        date_trunc('month', convert_timezone('UTC', 'America/New_York', start_time)) as call_month,
        convert_timezone('UTC', 'America/New_York', start_time)::date as call_date,
        
        -- JSON parsing for additional insights
        try_parse_json(ringcentral_json):to:location::string as to_location,
        try_parse_json(ringcentral_json):from:location::string as from_location,
        try_parse_json(ringcentral_json):from:device:id::string as from_device_id,
        try_parse_json(ringcentral_json):billing:costIncluded::boolean as cost_included,
        try_parse_json(ringcentral_json):internalType::string as internal_type,
        
        -- Metadata
        ringcentral_json,
        load_datetime,
        record_datetime
        
    from ingest_data
)

select * from transformed
