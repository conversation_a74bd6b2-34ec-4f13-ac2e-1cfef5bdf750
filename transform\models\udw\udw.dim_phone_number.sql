{{ config(
    alias = 'dim_phone_number'
) }}

with external_phone_numbers as (
    -- Get all unique external phone numbers (non-employee)
    select distinct
        from_phone_num as phone_number,
        from_location as location,
        'Outbound Target' as phone_type
    from {{ ref('ods.ringcentral_call_log') }}
    where from_emp_upn is null
      and from_phone_num is not null

    union all

    select distinct
        to_phone_num as phone_number,
        to_location as location,
        'Inbound Source' as phone_type
    from {{ ref('ods.ringcentral_call_log') }}
    where to_emp_upn is null
      and to_phone_num is not null
),

deduplicated_phones as (
    select
        phone_number,
        -- Take the first non-null location if multiple exist
        first_value(location ignore nulls) over (
            partition by phone_number
            order by case when location is not null then 1 else 2 end
        ) as location,
        -- Combine phone types if number appears in both contexts
        listagg(distinct phone_type, ', ') within group (order by phone_type) as phone_types
    from external_phone_numbers
    group by phone_number, location
),

final_phones as (
    select distinct
        phone_number,
        location,
        phone_types
    from deduplicated_phones
),

dimension_table as (
    select
        hash(phone_number) as phone_number_key,
        phone_number,
        coalesce(location, 'Unknown') as location,
        phone_types,

        -- Parse location for geographic analysis
        case
            when location like '%,%' then split_part(location, ',', -1)
            else null
        end as state_province,

        case
            when location like '%,%' then trim(split_part(location, ',', 1))
            else location
        end as city,

        -- Classification placeholders for future enhancement
        null as contact_type,  -- Future: 'Candidate', 'Client', 'Vendor', 'Other'
        null as company_name,  -- Future: Mapped company name
        null as contact_name,  -- Future: Mapped contact name

        -- Activity metrics (could be calculated in future)
        0 as total_calls_received,
        0 as total_calls_made,
        null as first_call_date,
        null as last_call_date,

        -- Metadata
        current_timestamp() as created_at,
        current_timestamp() as updated_at

    from final_phones
)

select * from dimension_table
