version: 2

models:
  - name: ods.activity_credit
    description: "Activity credit attribution to employees"
  - name: ods.activity
    description: "All activities"
  - name: ods.business_center
    description: "Units of business, profit/loss centers"
  - name: ods.calendar
    description: "Calendar reference"
  - name: ods.communication
    description: "Communication activities"
  - name: ods.ringcentral_call_log
    description: "RingCentral call log with business transformations and time zone conversions"
    columns:
      - name: session_id
        description: "Primary unique identifier for each call session"
        tests:
          - unique:
              name: ods_ringcentral_call_log_session_id_unique
          - not_null:
              name: ods_ringcentral_call_log_session_id_not_null
      - name: call_classification
        description: "Business classification of call type"
        tests:
          - accepted_values:
              values: ['Internal', 'Employee Outbound', 'Employee Inbound', 'External']
              name: ods_ringcentral_call_log_classification_valid
      - name: interaction_type
        description: "Interaction meaningfulness based on duration"
        tests:
          - accepted_values:
              values: ['Meaningful', 'Brief', 'No Connection']
              name: ods_ringcentral_call_log_interaction_type_valid
  # - name: prep.employee
  #   description: "Prep employee which is a super-set merge of Edge_Users(CMDBUSER) and UNV_Employee, established EMPLOYEE_ID as unique PK"