version: 2

sources:
  - name: ringcentral
    database: DLUKIC_DEV
    schema: RINGCENTRAL
    description: "RingCentral phone call log data - employee call activity and outcomes"
    loader: RingCentral API
    tables:
      - name: call_log
        identifier: CALL_LOG
        description: "Complete call log data from RingCentral including employee call activity, outcomes, and metadata"
        columns:
          - name: session_id
            data_type: varchar
            description: "Primary unique identifier for each call session"
          - name: call_record_id
            data_type: varchar
            description: "Alternative unique identifier for call records"
          - name: telephony_session_id
            data_type: varchar
            description: "Telephony system session identifier"
          - name: start_time
            data_type: timestamp_ntz
            description: "Call start time in UTC"
          - name: duration
            data_type: number
            description: "Call duration in seconds"
          - name: created_timestamp
            data_type: timestamp_ntz
            description: "Record creation timestamp"
          - name: from_emp_upn
            data_type: varchar
            description: "Calling employee User Principal Name (UPN)"
          - name: from_entra_id
            data_type: varchar
            description: "Calling employee Entra ID (Azure AD)"
          - name: from_phone_num
            data_type: varchar
            description: "Calling phone number"
          - name: to_emp_upn
            data_type: varchar
            description: "Receiving employee User Principal Name (UPN)"
          - name: to_entra_id
            data_type: varchar
            description: "Receiving employee Entra ID (Azure AD)"
          - name: to_phone_num
            data_type: varchar
            description: "Receiving phone number"
          - name: direction
            data_type: varchar
            description: "Call direction: 'I' for Inbound, 'O' for Outbound"
          - name: call_type
            data_type: varchar
            description: "Type of call (Voice, etc.)"
          - name: action
            data_type: varchar
            description: "Call action performed"
          - name: result
            data_type: varchar
            description: "Call outcome result (Call connected, Missed, Voicemail, etc.)"
          - name: ringcentral_json
            data_type: text
            description: "Complete JSON payload from RingCentral with additional metadata"
          - name: recording_id
            data_type: text
            description: "Recording identifier for the call"
          - name: from_ext
            data_type: text
            description: "Extension number for calling party"
          - name: to_ext
            data_type: text
            description: "Extension number for receiving party"
