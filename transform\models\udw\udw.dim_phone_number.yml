version: 2

models:
  - name: udw.dim_phone_number
    description: "Phone number dimension for external phone numbers with location and classification data"
    config:
      alias: dim_phone_number
    columns:
      - name: phone_number_key
        description: "Unique surrogate key for phone number dimension"
        tests:
          - unique:
              name: udw_dim_phone_number_key_unique
          - not_null:
              name: udw_dim_phone_number_key_not_null
      - name: phone_number
        description: "The actual phone number"
        tests:
          - not_null:
              name: udw_dim_phone_number_not_null
          - unique:
              name: udw_dim_phone_number_unique
      - name: location
        description: "Geographic location of the phone number (from RingCentral data)"
      - name: phone_types
        description: "Types of calls associated with this number (Outbound Target, Inbound Source)"
      - name: state_province
        description: "State or province extracted from location"
      - name: city
        description: "City extracted from location"
      - name: contact_type
        description: "Classification of contact type (placeholder for future enhancement)"
      - name: company_name
        description: "Associated company name (placeholder for future mapping)"
      - name: contact_name
        description: "Associated contact name (placeholder for future mapping)"
      - name: total_calls_received
        description: "Total number of calls received from this number"
      - name: total_calls_made
        description: "Total number of calls made to this number"
      - name: first_call_date
        description: "Date of first call interaction"
      - name: last_call_date
        description: "Date of last call interaction"
