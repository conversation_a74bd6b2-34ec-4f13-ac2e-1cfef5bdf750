version: 2

models:
  - name: udw.dim_call_outcome
    description: "Call outcome dimension containing all possible call results and their classifications"
    config:
      alias: dim_call_outcome
    columns:
      - name: call_outcome_key
        description: "Unique surrogate key for call outcome dimension"
        tests:
          - unique:
              name: udw_dim_call_outcome_key_unique
          - not_null:
              name: udw_dim_call_outcome_key_not_null
      - name: call_result
        description: "Original call result from RingCentral (Call connected, Missed, etc.)"
        tests:
          - not_null:
              name: udw_dim_call_outcome_result_not_null
      - name: call_outcome_category
        description: "Business category grouping of call results"
        tests:
          - accepted_values:
              values: ['Successful', 'No Answer', 'Voicemail', 'Data Quality Issue', 'Failed']
              name: udw_dim_call_outcome_category_valid
      - name: interaction_type
        description: "Interaction type based on duration thresholds"
        tests:
          - accepted_values:
              values: ['Meaningful', 'Brief', 'No Connection']
              name: udw_dim_call_outcome_interaction_type_valid
      - name: is_successful
        description: "Flag indicating if call was successful (1/0)"
      - name: is_voicemail
        description: "Flag indicating if call went to voicemail (1/0)"
      - name: is_no_answer
        description: "Flag indicating if call was not answered (1/0)"
      - name: is_data_quality_issue
        description: "Flag indicating if call had data quality issues (1/0)"
      - name: is_failed
        description: "Flag indicating if call failed (1/0)"
      - name: is_meaningful_interaction
        description: "Flag indicating if interaction was meaningful (1/0)"
      - name: is_brief_interaction
        description: "Flag indicating if interaction was brief (1/0)"
      - name: is_no_connection
        description: "Flag indicating if there was no connection (1/0)"
