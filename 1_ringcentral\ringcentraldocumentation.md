# RingCentral Call Log Implementation Documentation

## Overview
This document provides comprehensive documentation for the RingCentral call log data pipeline implementation, covering the complete data flow from source ingestion through to Power BI reporting.

## Architecture Overview

### Data Flow
```
RingCentral Source → Ingest Layer → ODS Layer → UDW Layer → Power BI
```

**Pipeline Stages:**
1. **Source**: RingCentral call log data in `DLUKIC_DEV.RINGCENTRAL.CALL_LOG`
2. **Ingest**: Raw data extraction with data quality checks (`ingest.ringcentral_call_log`)
3. **ODS**: Business transformations and time zone conversions (`ods.ringcentral_call_log`)
4. **UDW**: Dimensional model with fact and dimension tables
5. **Power BI**: Semantic layer for reporting and analytics

## Data Model Design

### Grain Definition
**Fact Table Grain**: One record per phone call session

### Key Business Rules
- **Meaningful Interaction**: Calls with duration ≥ 30 seconds
- **Successful Calls**: Results in 'Call connected' or 'Accepted'
- **Internal Calls**: Both FROM and TO participants are employees
- **Time Zone**: All times converted from UTC to America/New_York for reporting

## Implementation Details

### 1. Source Configuration
**File**: `transform/models/source/ringcentral_source.yml`

```yaml
sources:
  - name: ringcentral
    database: DLUKIC_DEV
    schema: RINGCENTRAL
    tables:
      - name: call_log
        identifier: CALL_LOG
```

**Key Fields**:
- `session_id`: Primary unique identifier
- `start_time`: Call start time (UTC)
- `duration`: Call duration in seconds
- `from_emp_upn`/`to_emp_upn`: Employee identifiers
- `result`: Call outcome
- `ringcentral_json`: Rich metadata payload

### 2. Ingest Layer
**File**: `transform/models/ingest/ingest.ringcentral_call_log.sql`

**Purpose**: Raw data extraction with data quality validation

**Key Features**:
- Deduplication by `session_id` (keeps most recent)
- Data quality filtering (valid directions, non-negative durations)
- Data quality status tracking
- Null value handling

**Data Quality Checks**:
- Session ID uniqueness validation
- Start time presence validation
- Direction values ('I'/'O' only)
- Duration >= 0 validation

### 3. ODS Layer
**File**: `transform/models/ods/ods.ringcentral_call_log.sql`

**Purpose**: Business transformations and enrichment

**Key Transformations**:

#### Time Zone Conversion
```sql
convert_timezone('UTC', 'America/New_York', start_time) as start_time_local
```

#### Interaction Classification
```sql
case
    when duration >= 30 then 'Meaningful'
    when duration > 0 then 'Brief'
    else 'No Connection'
end as interaction_type
```

#### Call Outcome Categorization
```sql
case
    when result in ('Call connected', 'Accepted') then 'Successful'
    when result in ('Missed', 'No Answer', 'Busy') then 'No Answer'
    when result = 'Voicemail' then 'Voicemail'
    when result = 'Wrong Number' then 'Data Quality Issue'
    else 'Failed'
end as call_outcome_category
```

#### Call Classification
```sql
case
    when from_emp_upn is not null and to_emp_upn is not null then 'Internal'
    when from_emp_upn is not null and direction = 'O' then 'Employee Outbound'
    when to_emp_upn is not null and direction = 'I' then 'Employee Inbound'
    else 'External'
end as call_classification
```

#### JSON Parsing
- Location data extraction
- Device information
- Billing details
- Internal type classification

### 4. UDW Dimensional Model

#### Fact Table: `udw.fact_call_activity`
**File**: `transform/models/udw/udw.fact_call_activity.sql`

**Grain**: One record per phone call

**Dimensions**:
- `from_employee_key`: Calling employee
- `to_employee_key`: Receiving employee
- `call_outcome_key`: Call result classification
- `call_date_key`: Date dimension
- `from_phone_number_key`: External calling number
- `to_phone_number_key`: External receiving number

**Measures**:
- `duration_seconds`: Call duration
- `call_count`: Count of calls (always 1)
- `is_connected_flag`: Success indicator
- `is_meaningful_interaction_flag`: Meaningful interaction indicator
- `is_internal_call_flag`: Internal call indicator

#### Dimension: `udw.dim_call_outcome`
**File**: `transform/models/udw/udw.dim_call_outcome.sql`

**Purpose**: Call result classifications and success indicators

**Key Attributes**:
- `call_result`: Original RingCentral result
- `call_outcome_category`: Business grouping
- `interaction_type`: Duration-based classification
- Success/failure flags

#### Dimension: `udw.dim_phone_number`
**File**: `transform/models/udw/udw.dim_phone_number.sql`

**Purpose**: External phone number registry with location data

**Key Attributes**:
- `phone_number`: The actual phone number
- `location`: Geographic location from RingCentral
- `city`/`state_province`: Parsed location components
- Future enhancement placeholders for contact/company mapping

## Data Quality & Testing

### Automated Tests
- **Uniqueness**: Session ID uniqueness across all layers
- **Referential Integrity**: Foreign key relationships
- **Data Validity**: Accepted values for categorical fields
- **Completeness**: Not null constraints on critical fields

### Data Quality Monitoring
- Duplicate session ID detection
- Invalid direction codes
- Negative duration values
- Missing employee mappings

## Business Metrics & KPIs

### Primary Metrics
1. **Call Volume**: Total calls by employee, day, time
2. **Connection Rate**: % of calls resulting in connection
3. **Meaningful Interaction Rate**: % of calls exceeding 30-second threshold
4. **Employee Activity**: Calls per employee per day/week

### Advanced Analytics
1. **Time-based Analysis**: Success rates by hour/day of week
2. **Duration Analysis**: Average call length trends
3. **Geographic Analysis**: Success rates by location
4. **Employee Performance**: Individual activity profiling

## Power BI Integration

### Recommended Measures
```dax
Total Calls = COUNT(fact_call_activity[call_count])
Connected Calls = CALCULATE([Total Calls], fact_call_activity[is_connected_flag] = 1)
Connection Rate = DIVIDE([Connected Calls], [Total Calls], 0)
Meaningful Interactions = CALCULATE([Total Calls], fact_call_activity[is_meaningful_interaction_flag] = 1)
Average Duration = AVERAGE(fact_call_activity[duration_seconds])
```

### Key Reports
1. **Executive Dashboard**: High-level KPIs and trends
2. **Employee Activity Report**: Individual performance metrics
3. **Operational Analysis**: Time-based success patterns
4. **Geographic Analysis**: Location-based insights

## Deployment & Maintenance

### Dependencies
- Employee dimension (`udw.dim_employee`) for employee mapping
- Date dimension for time-based analysis
- dbt_utils package for surrogate key generation

### Refresh Strategy
- **Frequency**: Daily incremental refresh
- **Lookback**: 7-day window for late-arriving data
- **Full Refresh**: Weekly for data quality validation

### Monitoring
- Data quality test results
- Row count validation
- Processing time monitoring
- Error rate tracking

## Future Enhancements

### Phase 2 Enhancements
1. **External Data Mapping**: Candidate/client phone number classification
2. **Advanced JSON Parsing**: Additional metadata extraction
3. **Predictive Analytics**: Call success prediction models
4. **Real-time Dashboards**: Near real-time call activity monitoring

### Technical Improvements
1. **Partitioning**: Date-based partitioning for performance
2. **Incremental Models**: Optimize for large data volumes
3. **Data Lineage**: Enhanced documentation and lineage tracking
4. **Automated Alerting**: Data quality issue notifications

## Troubleshooting

### Common Issues
1. **Missing Employee Mappings**: Check UPN/Entra ID alignment
2. **Duplicate Session IDs**: Investigate source data quality
3. **Time Zone Issues**: Verify UTC conversion logic
4. **Performance Issues**: Consider partitioning and indexing

### Support Contacts
- **Technical Issues**: Data Engineering Team
- **Business Logic**: Business Analysts
- **Power BI Reports**: BI Development Team

## File Structure

### Created Files
```
transform/models/
├── source/
│   └── ringcentral_source.yml              # Source configuration
├── ingest/
│   ├── ingest.ringcentral_call_log.sql     # Raw data ingestion
│   └── ingest.yml                          # Updated with RingCentral model
├── ods/
│   ├── ods.ringcentral_call_log.sql        # Business transformations
│   └── ods.yml                             # Updated with RingCentral model
└── udw/
    ├── udw.dim_call_outcome.sql            # Call outcome dimension
    ├── udw.dim_call_outcome.yml            # Dimension documentation
    ├── udw.dim_phone_number.sql            # Phone number dimension
    ├── udw.dim_phone_number.yml            # Dimension documentation
    ├── udw.fact_call_activity.sql          # Main fact table
    └── udw.fact_call_activity.yml          # Fact table documentation

1_ringcentral/
├── planning.md                             # Strategic planning document
├── taskbreakdown.md                        # Detailed implementation tasks
├── observations&insights.md                # Data analysis insights
└── ringcentraldocumentation.md             # This comprehensive guide
```

## Sample Queries

### Basic Call Volume Analysis
```sql
-- Daily call volume by employee
SELECT
    from_emp_upn,
    call_date,
    COUNT(*) as total_calls,
    SUM(is_connected_flag) as connected_calls,
    AVG(duration_seconds) as avg_duration
FROM udw.fact_call_activity
WHERE from_emp_upn IS NOT NULL
GROUP BY from_emp_upn, call_date
ORDER BY call_date DESC, total_calls DESC;
```

### Success Rate by Time of Day
```sql
-- Connection rate by hour of day
SELECT
    call_hour,
    COUNT(*) as total_calls,
    SUM(is_connected_flag) as connected_calls,
    ROUND(SUM(is_connected_flag) * 100.0 / COUNT(*), 2) as connection_rate_pct
FROM udw.fact_call_activity
GROUP BY call_hour
ORDER BY call_hour;
```

### Meaningful Interaction Analysis
```sql
-- Meaningful interactions by call classification
SELECT
    call_classification,
    COUNT(*) as total_calls,
    SUM(is_meaningful_interaction_flag) as meaningful_calls,
    ROUND(SUM(is_meaningful_interaction_flag) * 100.0 / COUNT(*), 2) as meaningful_rate_pct
FROM udw.fact_call_activity
GROUP BY call_classification
ORDER BY meaningful_rate_pct DESC;
```

## Data Validation Queries

### Data Quality Checks
```sql
-- Check for data quality issues
SELECT
    'Session ID Duplicates' as check_type,
    COUNT(*) - COUNT(DISTINCT session_id) as issue_count
FROM udw.fact_call_activity

UNION ALL

SELECT
    'Negative Durations' as check_type,
    COUNT(*) as issue_count
FROM udw.fact_call_activity
WHERE duration_seconds < 0

UNION ALL

SELECT
    'Invalid Directions' as check_type,
    COUNT(*) as issue_count
FROM udw.fact_call_activity
WHERE direction_code NOT IN ('I', 'O');
```

### Volume Validation
```sql
-- Compare record counts across layers
SELECT
    'Source' as layer,
    COUNT(*) as record_count
FROM DLUKIC_DEV.RINGCENTRAL.CALL_LOG

UNION ALL

SELECT
    'Ingest' as layer,
    COUNT(*) as record_count
FROM ingest.ringcentral_call_log

UNION ALL

SELECT
    'ODS' as layer,
    COUNT(*) as record_count
FROM ods.ringcentral_call_log

UNION ALL

SELECT
    'UDW' as layer,
    COUNT(*) as record_count
FROM udw.fact_call_activity;
```

## Performance Optimization

### Recommended Indexes
```sql
-- Fact table indexes for common query patterns
CREATE INDEX idx_fact_call_activity_date
ON udw.fact_call_activity (call_date);

CREATE INDEX idx_fact_call_activity_employee
ON udw.fact_call_activity (from_employee_key, call_date);

CREATE INDEX idx_fact_call_activity_outcome
ON udw.fact_call_activity (call_outcome_key, call_date);
```

### Partitioning Strategy
```sql
-- Consider date-based partitioning for large volumes
ALTER TABLE udw.fact_call_activity
ADD PARTITION BY (call_date);
```

---

*Last Updated: June 27, 2025*
*Version: 1.0*
*Author: Data Engineering Team*
