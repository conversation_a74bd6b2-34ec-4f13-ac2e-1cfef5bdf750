
version: 2

models:
  - name: ingest.activity
    description: "Activities"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_activity_id_unique
          - not_null:
              name: ingest_activity_id_not_null
  - name: ingest.accounts_payable_invoice_detail
    description: "Details for vendor invoice payments"
    columns:
      - name: id
        description: "The primary key for this table, rownum"
        tests:
          - unique:
              name: ingest_accounts_payable_invoice_detail_tid_unique
          - not_null:
              name: ingest_accounts_payable_invoice_detail_tid_not_null
  - name: ingest.cir_detail_split
    description: "Split detail table, spread split breaks per employee"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_cir_detail_split_id_unique
          - not_null:
              name: ingest_cir_detail_split_id_not_null
  - name: ingest.cir_location_department
    description: "CIR Location Department"
  - name: ingest.cir_master_split
    description: "Split master table, master split for a placement "
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_cir_master_split_id_unique
          - not_null:
              name: ingest_cir_master_split_id_not_null
  - name: ingest.communication
    description: "Communications"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_communication_id_unique
          - not_null:
              name: ingest_communication_id_not_null
  - name: ingest.currency_rates
    description: "Currency exchange rates between USD and CAD"
  - name: ingest.person
    description: "People base"
  - name: ingest.company
    description: "Company base"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_company_id_unique
          - not_null:
              name: ingest_company_id_not_null
  - name: ingest.employee_tenure
    description: "Employee asignment history"
  - name: ingest.division
    description: "Division base model"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_division_id_unique
          - not_null:
              name: ingest_division_id_not_null
  - name: ingest.division_location
    description: "Division Location base model"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_division_location_id_unique
          - not_null:
              name: ingest_division_location_id_not_null
  - name: ingest.edge_user
    description: "Edge users"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_edge_user_id_unique
          - not_null:
              name: ingest_edge_user_id_not_null
  - name: ingest.forecast
    description: "JEMS forecasts"
  - name: ingest.interview
    description: "Interviews"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_interview_id_unique
          - not_null:
              name: ingest_interview_id_not_null
  - name: ingest.interview_type
    description: "Interview types"
  - name: ingest.interview_result_type
    description: "Interview result types"
  - name: ingest.hours_bill_rates
    description: "Hours & bill rates for contract job orders"
  - name: ingest.hours_bill_rates_can
    description: "Hours & bill rates for contract job orders in Canada"
  - name: ingest.jems
    description: "JEMS forecasts"
  - name: ingest.job_order
    description: "Job order base"
    columns:
      - name: id
        description: "Job order ID, primary unique identifier"
        tests:
          - unique:
              name: ingest_job_order_id_unique
          - not_null:
              name: ingest_job_order_id_not_null
  - name: ingest.job_order_type
    description: "JobOrder types"
  - name: ingest.job_order_contact
    description: "Job order contact with role"
  - name: ingest.location
    description: "Location base model"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_location_id_unique
          - not_null:
              name: ingest_location_id_not_null
  - name: ingest.mapped_user_interview
    description: "User to Interview mapping"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_mapped_user_interview_id_unique
          - not_null:
              name: ingest_mapped_user_interview_id_not_null
  - name: ingest.mapped_user_placement
    description: "User to Placement mapping"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_mapped_user_placement_id_unique
          - not_null:
              name: ingest_mapped_user_placement_id_not_null
  - name: ingest.mapped_user_submittal
    description: "User to Submittal mapping"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_mapped_user_submittal_id_unique
          - not_null:
              name: ingest_mapped_user_submittal_id_not_null
  - name: ingest.placement
    description: "Placements"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_placement_id_unique
          - not_null:
              name: ingest_placement_id_not_null
  - name: ingest.position_title
    description: "Position titles"
  - name: ingest.staff_role
    description: "Base staff roles used in activity-user mappings and credit attribution"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_staff_role_id_unique
          - not_null:
              name: ingest_staff_role_id_not_null
  - name: ingest.submittal
    description: "Submittals"
    columns:
      - name: id
        description: "The primary key for this table"
        tests:
          - unique:
              name: ingest_submittal_id_unique
          - not_null:
              name: ingest_submittal_id_not_null
  - name: ingest.submittal_type
    description: "Submittal type values"
  - name: ingest.submittal_result
    description: "Submittal result values"
  - name: ingest.unv_employee
    description: "unv employee"
  - name: ingest.ringcentral_call_log
    description: "RingCentral call log data with data quality checks and deduplication"
    columns:
      - name: session_id
        description: "Primary unique identifier for each call session"
        tests:
          - unique:
              name: ingest_ringcentral_call_log_session_id_unique
          - not_null:
              name: ingest_ringcentral_call_log_session_id_not_null
      - name: data_quality_status
        description: "Data quality validation status"
        tests:
          - accepted_values:
              values: ['Valid']
              name: ingest_ringcentral_call_log_data_quality_valid
