{{ config(
    alias = 'billing_unified_forecast'
) }}

{%- set forecast_start_date_var = var('forecast_start_date', none) -%}
{%- if forecast_start_date_var is not none and forecast_start_date_var != 'current_date()' -%}
    {%- set forecast_start_date_expr = "TO_DATE('" ~ forecast_start_date_var ~ "')" -%}
{%- else -%}
    {%- set forecast_start_date_expr = 'current_date()' -%}
{%- endif -%}

/*
==============================
Billing Unified Forecast
==============================
- Analyzes historical billing patterns
- Determines forecast eligibility based on recent activity
- Calculates average metrics needed for forecasting
- Accepts configurable forecast_start_date for backtesting
*/

with historical_billing as (
    select
        *, -- Select all columns from the source
        -- Add flags for data quality issues
        case when bill_hours < 0 then true else false end as is_negative_hours,
        case when bill_rate_amount < 0 then true else false end as is_negative_bill_rate,
        case when cost_rate_amount < 0 then true else false end as is_negative_cost_rate,
        case when bill_rate_amount >= 5000 then true else false end as is_high_bill_rate_outlier, -- Flag extreme outliers based on analysis
        case when cost_rate_amount >= 1000 then true else false end as is_high_cost_rate_outlier, -- Flag extreme outliers based on analysis
        case when bill_hours >= 80 then true else false end as is_high_hours_outlier -- Flag extreme hours outliers
    from {{ ref('prep.billing_unified') }}
    where billing_date < {{ forecast_start_date_expr }} -- Only use data before forecast start date
        and billing_date >= '2024-01-01' -- Focus on recent data only (2024-2025)
    -- Removing strict filters here to add flags instead
),

historical_billing_filtered as (
    select *
    from historical_billing
    where not is_negative_hours
      and not is_negative_bill_rate
      and not is_negative_cost_rate
      and not is_high_bill_rate_outlier
      and not is_high_cost_rate_outlier
      and not is_high_hours_outlier
      and not (bill_hours = 0 and spread_amount != 0) -- Exclude 0-hour records with non-zero spread
),

-- Calculate previous week's spread for trend analysis
historical_spread_with_lag as (
    select
        *,
        lag(spread_amount) over (partition by placement_id order by billing_date) as prev_week_spread,
        max(billing_date) over (partition by placement_id) as placement_max_billing_date
    from historical_billing_filtered
),

-- Calculate recent spread trend (average weekly percentage change over last 8 weeks)
spread_trend_calculation as (
    select
        placement_id,
        -- Cap the trend calculation between -2 and 2 (i.e., -200% to +200% change)
        greatest(-2.0, least(2.0, avg(case when prev_week_spread != 0 then (spread_amount - prev_week_spread) / nullif(prev_week_spread, 0) else 0 end))) as recent_spread_trend
    from historical_spread_with_lag
    where billing_date >= dateadd('week', -8, placement_max_billing_date)
    group by placement_id
),

placement_aggregates as (
    select
        placement_id,
        job_order_id,
        client_id,
        candidate_id,
        employee_id,
        business_center_id,
        employee_role_id,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY bill_hours) as median_weekly_hours,
        -- Use effective rates calculated from amounts, not stored rates
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY bill_amount / NULLIF(bill_hours, 0)) as median_bill_rate,
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY cost_amount / NULLIF(bill_hours, 0)) as median_cost_rate,
        max(billing_date) as last_billing_date,
        count(*) as billing_record_count,
        avg(bill_hours) as avg_weekly_hours,
        stddev(bill_hours) as stddev_weekly_hours,
        stddev(bill_amount / NULLIF(bill_hours, 0)) as bill_rate_stddev,
        stddev(cost_amount / NULLIF(bill_hours, 0)) as cost_rate_stddev,
        avg(bill_amount / NULLIF(bill_hours, 0)) as avg_bill_rate,
        avg(cost_amount / NULLIF(bill_hours, 0)) as avg_cost_rate
    from historical_billing_filtered
    where bill_hours > 0  -- Ensure we don't divide by zero
    group by 1, 2, 3, 4, 5, 6, 7
),

latest_values as (
    select
        placement_id,
        first_value(bill_amount / NULLIF(bill_hours, 0)) over (partition by placement_id order by billing_date desc) as latest_bill_rate,
        first_value(cost_amount / NULLIF(bill_hours, 0)) over (partition by placement_id order by billing_date desc) as latest_cost_rate,
        first_value(bill_hours) over (partition by placement_id order by billing_date desc) as latest_hours
    from historical_billing_filtered
    where bill_hours > 0  -- Ensure we don't divide by zero
    qualify row_number() over (partition by placement_id order by billing_date desc) = 1
),

recent_average_rates as (
    select
        placement_id,
        billing_date,
        avg(case when not is_negative_bill_rate and not is_high_bill_rate_outlier and bill_hours > 0 then bill_amount / bill_hours end) over (partition by placement_id order by billing_date rows between 3 preceding and current row) as recent_avg_bill_rate,
        avg(case when not is_negative_cost_rate and not is_high_cost_rate_outlier and bill_hours > 0 then cost_amount / bill_hours end) over (partition by placement_id order by billing_date rows between 3 preceding and current row) as recent_avg_cost_rate,
        -- Calculate recent average hours (last 4 non-outlier records)
        avg(case when not is_negative_hours then bill_hours end) over (partition by placement_id order by billing_date rows between 3 preceding and current row) as recent_avg_hours
    from historical_billing
),

-- Calculate recent weekly hours patterns (last 6 weeks for current patterns)
recent_weekly_patterns as (
    select
        placement_id,
        avg(weekly_hours) as recent_avg_weekly_hours,
        median(weekly_hours) as recent_median_weekly_hours,
        max(weekly_hours) as recent_max_weekly_hours,
        count(*) as recent_weeks_count
    from (
        select
            placement_id,
            date_trunc('week', billing_date) as week_date,
            sum(bill_hours) as weekly_hours
        from historical_billing_filtered
        where billing_date >= dateadd('week', -6, {{ forecast_start_date_expr }})
        group by placement_id, date_trunc('week', billing_date)
    )
    group by placement_id
),

-- Calculate recent rate patterns (last 6 weeks for current market rates)
recent_rate_patterns as (
    select
        placement_id,
        avg(bill_amount / NULLIF(bill_hours, 0)) as recent_avg_bill_rate,
        median(bill_amount / NULLIF(bill_hours, 0)) as recent_median_bill_rate,
        avg(cost_amount / NULLIF(bill_hours, 0)) as recent_avg_cost_rate,
        median(cost_amount / NULLIF(bill_hours, 0)) as recent_median_cost_rate,
        count(*) as recent_rate_records,
        max(billing_date) as most_recent_billing_date
    from historical_billing_filtered
    where billing_date >= dateadd('week', -6, {{ forecast_start_date_expr }})
        and not is_negative_bill_rate
        and not is_negative_cost_rate
        and not is_high_bill_rate_outlier
        and not is_high_cost_rate_outlier
        and bill_hours > 0  -- Ensure we don't divide by zero
        and bill_hours <= 50  -- Filter out data quality issues
    group by placement_id
),

-- Calculate market trend adjustments based on time periods
market_trend_adjustments as (
    select
        -- Calculate trend factors based on when the forecast is being made
        case
            when {{ forecast_start_date_expr }} >= '2025-06-01' then 1.08  -- Recent trend shows higher rates
            when {{ forecast_start_date_expr }} >= '2025-03-01' then 1.05  -- Moderate increase
            when {{ forecast_start_date_expr }} >= '2025-01-01' then 1.02  -- Slight increase
            else 1.0  -- No adjustment for 2024
        end as spread_rate_trend_factor,

        case
            when {{ forecast_start_date_expr }} >= '2025-06-01' then 0.88  -- More conservative for recent periods
            when {{ forecast_start_date_expr }} >= '2025-03-01' then 0.92  -- Moderate conservative
            when {{ forecast_start_date_expr }} >= '2025-01-01' then 0.95  -- Slight conservative
            else 0.98  -- Minimal adjustment for 2024
        end as forecast_conservatism_factor
),

-- Combine aggregates with the latest values
placement_metrics as (
    select
        a.*, -- Select all columns from aggregates
        l.latest_bill_rate,
        l.latest_cost_rate,
        l.latest_hours,
        st.recent_spread_trend,
        rw.recent_avg_weekly_hours,
        rw.recent_median_weekly_hours,
        rw.recent_max_weekly_hours,
        rw.recent_weeks_count,
        rr.recent_avg_bill_rate,
        rr.recent_median_bill_rate,
        rr.recent_avg_cost_rate,
        rr.recent_median_cost_rate,
        rr.recent_rate_records,
        rr.most_recent_billing_date,
        mta.spread_rate_trend_factor,
        mta.forecast_conservatism_factor,

        -- Calculate rate change limits
        case
            when a.median_bill_rate > 0 then
                least(1.2, greatest(0.8, l.latest_bill_rate / a.median_bill_rate))
            else 1.0
        end as bill_rate_change_factor,
        -- Calculate hours stability
        case
            when a.avg_weekly_hours > 0 then
                a.stddev_weekly_hours / a.avg_weekly_hours
            else 0
        end as hours_variation_coefficient
    from placement_aggregates a
    left join latest_values l on a.placement_id = l.placement_id
    -- Join with historical_billing_with_recency (latest record) to get recent averages
    left join recent_average_rates h on a.placement_id = h.placement_id and h.billing_date = a.last_billing_date
    left join spread_trend_calculation st on a.placement_id = st.placement_id
    left join recent_weekly_patterns rw on a.placement_id = rw.placement_id
    left join recent_rate_patterns rr on a.placement_id = rr.placement_id
    cross join market_trend_adjustments mta
),

-- Use ods.placement instead of udw.dim_placement to break the circular dependency
placement_details as (
    select
        pm.*,
        p.estimated_end_date,
        p.falloff_date,
        p.start_date,
        case
            when p.estimated_end_date is not null and p.estimated_end_date > {{ forecast_start_date_expr }}
            then p.estimated_end_date
            else dateadd('month', 3, {{ forecast_start_date_expr }})
        end as forecast_end_date,
        case
            when pm.median_weekly_hours > 0
            and pm.median_bill_rate > 0
            and pm.last_billing_date >= dateadd('month', -6, {{ forecast_start_date_expr }}) -- Relaxed recent activity to 6 months
            and (p.falloff_date is null or p.falloff_date > {{ forecast_start_date_expr }})
            and p.start_date <= {{ forecast_start_date_expr }} -- Only started contracts
            and pm.billing_record_count >= 1 -- Relaxed minimum billing records (changed from 4 to 1)
            and pm.hours_variation_coefficient < 0.6 -- Relaxed stability check (changed from 0.4 to 0.6)
            and pm.bill_rate_stddev / nullif(pm.median_bill_rate, 0) < 0.4 -- Relaxed rate stability check (changed from 0.2 to 0.4)

            then true
            else false
        end as is_forecast_eligible,
        -- Add forecast confidence score
        case
            when pm.billing_record_count >= 12 then 1.0
            when pm.billing_record_count >= 8 then 0.8
            when pm.billing_record_count >= 4 then 0.6
            else 0.0
        end *
        (1 - pm.hours_variation_coefficient) *
        (1 - (pm.bill_rate_stddev / nullif(pm.median_bill_rate, 0))) as forecast_confidence_score
    from placement_metrics pm
    left join {{ ref('ods.placement') }} as p
        on pm.placement_id = p.id
)

select
    placement_id,
    job_order_id,
    client_id,
    candidate_id,
    employee_id,
    business_center_id,
    employee_role_id,
    median_weekly_hours,
    median_bill_rate,
    median_cost_rate,
    last_billing_date,
    billing_record_count,
    avg_weekly_hours,
    stddev_weekly_hours,
    bill_rate_stddev,
    cost_rate_stddev,
    avg_bill_rate,
    avg_cost_rate,
    latest_bill_rate,
    latest_cost_rate,
    latest_hours,
    bill_rate_change_factor,
    hours_variation_coefficient,
    estimated_end_date,
    falloff_date,
    start_date,
    forecast_end_date,
    is_forecast_eligible,
    forecast_confidence_score,
    recent_spread_trend,
    recent_avg_weekly_hours,
    recent_median_weekly_hours,
    recent_max_weekly_hours,
    recent_weeks_count,
    recent_avg_bill_rate,
    recent_median_bill_rate,
    recent_avg_cost_rate,
    recent_median_cost_rate,
    recent_rate_records,
    most_recent_billing_date,
    spread_rate_trend_factor,
    forecast_conservatism_factor
from placement_details
QUALIFY ROW_NUMBER() OVER (PARTITION BY placement_id ORDER BY last_billing_date DESC) = 1




