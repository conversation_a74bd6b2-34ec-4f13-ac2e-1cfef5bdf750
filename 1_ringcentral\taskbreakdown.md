# RingCentral Call Log Implementation - Task Breakdown

## Pre-Implementation Setup

### [ ] 1. Source Configuration (Priority: HIGH)
**Estimated Time**: 30 minutes
**Dependencies**: Client confirmation of production source location

**Tasks**:
- [ ] Confirm production source database/schema location with client
- [ ] Create new source configuration in dbt/pipeline
- [ ] Test connection to production source
- [ ] Validate source data structure matches sample
- [ ] Document any schema differences

**Acceptance Criteria**:
- [ ] Source connection established
- [ ] Source query returns expected data structure
- [ ] Documentation updated with source details

---

## Phase 1: Core Pipeline (Week 1)

### [ ] 2. Ingest Model Development
**Estimated Time**: 2 hours
**Dependencies**: Source configuration complete

**Tasks**:
- [ ] Create `ingest_ringcentral_call_log.sql`
- [ ] Implement weekly lookback with upsert logic
- [ ] Add data quality checks (null checks, duplicate detection)
- [ ] Test with sample data
- [ ] Implement error handling

**Key Fields to Extract**:
```sql
-- Core identification
session_id,
call_record_id,
telephony_session_id,

-- Timing
start_time,
duration,
created_timestamp,

-- Participants
from_emp_upn,
from_entra_id,
from_phone_num,
to_emp_upn,
to_entra_id,
to_phone_num,

-- Call details
direction,
call_type,
action,
result,

-- Metadata
ringcentral_json
```

**Data Quality Checks**:
- [ ] Session ID uniqueness validation
- [ ] Start time reasonableness check
- [ ] Duration >= 0 validation
- [ ] Direction values (O/I only)
- [ ] Employee UPN format validation

### [ ] 3. ODS Model Development
**Estimated Time**: 3 hours
**Dependencies**: Ingest model complete

**Tasks**:
- [ ] Create `ods_ringcentral_call_log.sql`
- [ ] Implement time zone conversion (UTC to local)
- [ ] Add calculated fields for analysis
- [ ] Parse basic JSON elements if needed
- [ ] Add business logic transformations

**Key Transformations**:
```sql
-- Time zone conversion
CONVERT_TIMEZONE('UTC', 'America/New_York', start_time) as start_time_local,

-- Duration classifications
CASE 
    WHEN duration >= 30 THEN 'Meaningful'
    WHEN duration > 0 THEN 'Brief'
    ELSE 'No Connection'
END as interaction_type,

-- Call success classification
CASE 
    WHEN result IN ('Call connected', 'Accepted') THEN 'Successful'
    WHEN result IN ('Missed', 'No Answer', 'Busy') THEN 'No Answer'
    WHEN result IN ('Voicemail') THEN 'Voicemail'
    ELSE 'Failed'
END as call_outcome_category,

-- Self-call detection
CASE 
    WHEN from_emp_upn IS NOT NULL AND to_emp_upn IS NOT NULL THEN 'Internal'
    WHEN from_emp_upn IS NOT NULL THEN 'Employee Outbound'
    WHEN to_emp_upn IS NOT NULL THEN 'Employee Inbound'
    ELSE 'External'
END as call_classification,

-- Time-based analysis fields
EXTRACT(HOUR FROM start_time_local) as call_hour,
EXTRACT(DOW FROM start_time_local) as call_day_of_week,
DATE_TRUNC('week', start_time_local) as call_week,
DATE_TRUNC('month', start_time_local) as call_month
```

### [ ] 4. UDW Fact Table Development
**Estimated Time**: 2 hours
**Dependencies**: ODS model complete

**Tasks**:
- [ ] Create `fact_call_activity.sql`
- [ ] Implement proper dimensional modeling
- [ ] Add measure calculations
- [ ] Create appropriate indexes
- [ ] Test aggregation performance

**Fact Table Structure**:
```sql
-- Dimensions (Foreign Keys)
call_date_key,
call_time_key,
from_employee_key,
to_employee_key,
call_outcome_key,

-- Degenerate Dimensions
session_id,
call_record_id,
from_phone_num,
to_phone_num,

-- Measures
duration_seconds,
call_count (always 1),
is_connected_flag,
is_meaningful_interaction_flag,
is_internal_call_flag,

-- Additional attributes
direction_code,
call_classification
```

---

## Phase 2: Dimensional Models (Week 1-2)

### [ ] 5. Dimension Table Development
**Estimated Time**: 3 hours
**Dependencies**: Fact table in progress

#### [ ] 5a. dim_call_outcome
```sql
CREATE TABLE dim_call_outcome AS
SELECT DISTINCT
    ROW_NUMBER() OVER (ORDER BY result) as call_outcome_key,
    result as call_result,
    call_outcome_category,
    CASE WHEN result IN ('Call connected', 'Accepted') THEN 1 ELSE 0 END as is_successful,
    CASE WHEN result = 'Voicemail' THEN 1 ELSE 0 END as is_voicemail,
    CASE WHEN result IN ('Missed', 'No Answer') THEN 1 ELSE 0 END as is_no_answer
FROM ods_ringcentral_call_log;
```

#### [ ] 5b. dim_employee (enhance existing or create)
- [ ] Map from existing CMDB User table
- [ ] Add Entra ID linkage
- [ ] Include department/role information

#### [ ] 5c. dim_phone_number (new)
- [ ] Extract unique external phone numbers
- [ ] Parse location data from JSON where available
- [ ] Add placeholder for future candidate/client mapping

### [ ] 6. Data Quality & Testing
**Estimated Time**: 2 hours

**Tasks**:
- [ ] Create comprehensive data quality tests
- [ ] Validate fact/dimension relationships
- [ ] Test historical data consistency
- [ ] Performance testing for large datasets
- [ ] Create monitoring alerts

**Key Tests**:
- [ ] Fact table grain validation (one record per call)
- [ ] Dimensional integrity checks
- [ ] Measure calculation validation
- [ ] Time zone conversion accuracy
- [ ] Employee mapping completeness

---

## Phase 3: Power BI Integration (Week 2-3)

### [ ] 7. Power BI Data Model
**Estimated Time**: 4 hours
**Dependencies**: UDW tables complete

**Tasks**:
- [ ] Create Power BI data source connections
- [ ] Build semantic model with proper relationships
- [ ] Create calculated measures and columns
- [ ] Implement row-level security if needed
- [ ] Test data refresh performance

**Key Measures**:
```dax
Total Calls = COUNT(fact_call_activity[call_count])
Connected Calls = CALCULATE(COUNT(fact_call_activity[call_count]), fact_call_activity[is_connected_flag] = 1)
Connection Rate = DIVIDE([Connected Calls], [Total Calls], 0)
Meaningful Interactions = CALCULATE(COUNT(fact_call_activity[call_count]), fact_call_activity[is_meaningful_interaction_flag] = 1)
Average Call Duration = AVERAGE(fact_call_activity[duration_seconds])
Calls per Employee per Day = DIVIDE([Total Calls], DISTINCTCOUNT(fact_call_activity[from_employee_key]) * DISTINCTCOUNT(fact_call_activity[call_date_key]))
```

### [ ] 8. Report Development
**Estimated Time**: 6 hours
**Dependencies**: Data model complete

#### [ ] 8a. Executive Dashboard
- [ ] High-level KPI cards (total calls, connection rate, etc.)
- [ ] Call volume trend by week/month
- [ ] Inbound vs Outbound split
- [ ] Top performing employees by calls/connections

#### [ ] 8b. Employee Activity Report
- [ ] Calls per employee per day/week
- [ ] Individual employee performance metrics
- [ ] Time-of-day activity heatmap
- [ ] Duration distribution analysis

#### [ ] 8c. Operational Analysis Report
- [ ] Success rate by time of day/day of week
- [ ] Call outcome distribution
- [ ] Duration threshold analysis
- [ ] Geographic analysis (if location data available)

### [ ] 9. Advanced Analytics Views
**Estimated Time**: 3 hours
**Dependencies**: Basic reports complete

**Tasks**:
- [ ] Peak calling hours analysis
- [ ] Call success prediction model
- [ ] Employee productivity benchmarking
- [ ] Trend analysis and forecasting
- [ ] Interactive filtering and drill-down capabilities

---

## Phase 4: Enhancement & Optimization (Week 3-4)

### [ ] 10. JSON Payload Analysis
**Estimated Time**: 4 hours
**Dependencies**: Core pipeline stable

**Tasks**:
- [ ] Parse JSON payload for additional insights
- [ ] Extract location data for geographic analysis
- [ ] Extract device information for technology insights
- [ ] Create additional dimensions if valuable data found

**Potential JSON Elements**:
```sql
-- Location data
JSON_EXTRACT_PATH_TEXT(ringcentral_json, 'to.location') as to_location,
JSON_EXTRACT_PATH_TEXT(ringcentral_json, 'from.location') as from_location,

-- Device information
JSON_EXTRACT_PATH_TEXT(ringcentral_json, 'from.device.id') as from_device_id,

-- Billing information
JSON_EXTRACT_PATH_TEXT(ringcentral_json, 'billing.costIncluded') as cost_included,

-- Internal type classification
JSON_EXTRACT_PATH_TEXT(ringcentral_json, 'internalType') as internal_type
```

### [ ] 11. External Data Integration
**Estimated Time**: 3 hours
**Dependencies**: Client provides mapping data

**Tasks**:
- [ ] Integrate candidate phone number mapping
- [ ] Integrate client contact mapping
- [ ] Create call classification logic
- [ ] Update reports with candidate/client analysis

### [ ] 12. Performance Optimization
**Estimated Time**: 2 hours

**Tasks**:
- [ ] Query performance optimization
- [ ] Index creation and tuning
- [ ] Power BI refresh optimization
- [ ] Partitioning strategy for large datasets

### [ ] 13. Documentation & Training
**Estimated Time**: 2 hours

**Tasks**:
- [ ] Create technical documentation
- [ ] Create user guide for Power BI reports
- [ ] Document business logic and calculations
- [ ] Create maintenance procedures

---

## Final Validation & Deployment

### [ ] 14. User Acceptance Testing
**Estimated Time**: 2 hours

**Tasks**:
- [ ] Validate metrics against existing Edge reports
- [ ] Business user testing of Power BI reports
- [ ] Performance testing under full data load
- [ ] Security and access validation

### [ ] 15. Production Deployment
**Estimated Time**: 1 hour

**Tasks**:
- [ ] Deploy pipeline to production
- [ ] Set up automated refresh schedules
- [ ] Configure monitoring and alerts
- [ ] Create backup and recovery procedures

---

## Critical Path & Dependencies

**Week 1 Critical Path**:
Source Setup → Ingest → ODS → Fact Table

**Week 2 Critical Path**:
Dimensions → Power BI Model → Basic Reports

**Week 3 Critical Path**:
Advanced Reports → JSON Analysis → Testing

**Week 4 Critical Path**:
External Integration → Optimization → Deployment

## Success Criteria

- [ ] All calls from source system are ingested and processed
- [ ] Data quality checks pass with >99% success rate
- [ ] Power BI reports load within 30 seconds
- [ ] Business users can answer all key questions from requirements
- [ ] System handles current data volume with room for 3x growth
- [ ] Automated refresh runs successfully daily