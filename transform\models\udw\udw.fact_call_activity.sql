{{ config(
    alias = 'fact_call_activity'
) }}

with call_data as (
    select * from {{ ref('ods.ringcentral_call_log') }}
),

-- Get dimension keys
call_outcome_dim as (
    select * from {{ ref('udw.dim_call_outcome') }}
),

phone_number_dim as (
    select * from {{ ref('udw.dim_phone_number') }}
),

-- Try to get employee dimensions (may not exist yet)
employee_dim as (
    {% if execute %}
        {% set employee_table_exists = adapter.get_relation(database=target.database, schema='udw', identifier='dim_employee') %}
        {% if employee_table_exists %}
    select
        employee_key,
        employee_upn,
        entra_id
    from {{ ref('udw.dim_employee') }}
        {% else %}
    select
        -1 as employee_key,
        'PLACEH<PERSON>DER' as employee_upn,
        'PLACEHOLDER' as entra_id
    where 1=0  -- Return no rows when table doesn't exist
        {% endif %}
    {% else %}
    select
        -1 as employee_key,
        '<PERSON>LACEHOLDER' as employee_upn,
        'PLACEHOLDER' as entra_id
    where 1=0  -- Return no rows during parsing
    {% endif %}
),

fact_table as (
    select
        -- Generate unique fact key
        {{ dbt_utils.generate_surrogate_key(['c.session_id']) }} as call_activity_key,

        -- Dimension foreign keys
        coalesce(from_emp.employee_key, -1) as from_employee_key,
        coalesce(to_emp.employee_key, -1) as to_employee_key,
        co.call_outcome_key,
        coalesce(from_phone.phone_number_key, -1) as from_phone_number_key,
        coalesce(to_phone.phone_number_key, -1) as to_phone_number_key,

        -- Date/time dimensions (assuming these exist)
        to_number(to_char(c.call_date, 'YYYYMMDD')) as call_date_key,
        c.call_hour * 100 as call_time_key,  -- Simple time key (HHMM format)

        -- Degenerate dimensions (high cardinality, stored in fact)
        c.session_id,
        c.call_record_id,
        c.telephony_session_id,
        c.from_phone_num,
        c.to_phone_num,

        -- Measures
        c.duration as duration_seconds,
        1 as call_count,
        c.is_connected_flag,
        c.is_meaningful_interaction_flag,
        c.is_internal_call_flag,

        -- Additional attributes
        c.direction as direction_code,
        c.call_classification,
        c.call_type,
        c.action,

        -- Time attributes for analysis
        c.start_time_utc,
        c.start_time_local,
        c.call_date,
        c.call_hour,
        c.call_day_of_week,
        c.call_week,
        c.call_month,

        -- Location information
        c.from_location,
        c.to_location,
        c.from_device_id,

        -- Business flags
        c.cost_included,
        c.internal_type,

        -- Additional source fields
        c.recording_id,
        c.from_ext,
        c.to_ext,

        -- Metadata
        c.created_timestamp,
        c.processed_at,
        current_timestamp() as fact_created_at

    from call_data c

    -- Join to call outcome dimension
    left join call_outcome_dim co
        on c.result = co.call_result

    -- Join to phone number dimensions
    left join phone_number_dim from_phone
        on c.from_phone_num = from_phone.phone_number
        and c.from_emp_upn is null  -- Only for external numbers

    left join phone_number_dim to_phone
        on c.to_phone_num = to_phone.phone_number
        and c.to_emp_upn is null  -- Only for external numbers

    -- Join to employee dimensions (if available)
    left join employee_dim from_emp
        on c.from_emp_upn = from_emp.employee_upn
        or c.from_entra_id = from_emp.entra_id

    left join employee_dim to_emp
        on c.to_emp_upn = to_emp.employee_upn
        or c.to_entra_id = to_emp.entra_id
)

select * from fact_table
