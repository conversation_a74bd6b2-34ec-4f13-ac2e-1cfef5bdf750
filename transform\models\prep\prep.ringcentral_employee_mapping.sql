{{ config(
    alias = 'ringcentral_employee_mapping'
) }}

-- This model creates a mapping of employees found in RingCentral data
-- to help with employee dimension integration

with ringcentral_employees as (
    select
        from_emp_upn as employee_upn,
        from_entra_id as entra_id
    from {{ ref('ods.ringcentral_call_log') }}
    where from_emp_upn is not null
    
    union
    
    select
        to_emp_upn as employee_upn,
        to_entra_id as entra_id
    from {{ ref('ods.ringcentral_call_log') }}
    where to_emp_upn is not null
),

deduplicated_employees as (
    select distinct
        employee_upn,
        entra_id
    from ringcentral_employees
    where employee_upn is not null
),

employee_stats as (
    select
        e.employee_upn,
        e.entra_id,
        
        -- Calculate call activity stats
        count(case when c.from_emp_upn = e.employee_upn then 1 end) as outbound_calls,
        count(case when c.to_emp_upn = e.employee_upn then 1 end) as inbound_calls,
        count(*) as total_calls,
        
        sum(case when c.from_emp_upn = e.employee_upn then c.duration else 0 end) as outbound_duration,
        sum(case when c.to_emp_upn = e.employee_upn then c.duration else 0 end) as inbound_duration,
        
        min(c.call_date) as first_call_date,
        max(c.call_date) as last_call_date,
        
        -- Success rates
        sum(case when c.from_emp_upn = e.employee_upn and c.is_connected_flag = 1 then 1 else 0 end) as successful_outbound_calls,
        sum(case when c.to_emp_upn = e.employee_upn and c.is_connected_flag = 1 then 1 else 0 end) as successful_inbound_calls
        
    from deduplicated_employees e
    left join {{ ref('ods.ringcentral_call_log') }} c
        on (c.from_emp_upn = e.employee_upn or c.to_emp_upn = e.employee_upn)
    group by e.employee_upn, e.entra_id
)

select
    employee_upn,
    entra_id,
    outbound_calls,
    inbound_calls,
    total_calls,
    outbound_duration,
    inbound_duration,
    first_call_date,
    last_call_date,
    successful_outbound_calls,
    successful_inbound_calls,
    
    -- Calculate success rates
    case 
        when outbound_calls > 0 then round(successful_outbound_calls * 100.0 / outbound_calls, 2)
        else 0 
    end as outbound_success_rate_pct,
    
    case 
        when inbound_calls > 0 then round(successful_inbound_calls * 100.0 / inbound_calls, 2)
        else 0 
    end as inbound_success_rate_pct,
    
    -- Activity classification
    case 
        when total_calls >= 100 then 'High Activity'
        when total_calls >= 50 then 'Medium Activity'
        when total_calls >= 10 then 'Low Activity'
        else 'Minimal Activity'
    end as activity_level,
    
    current_timestamp() as created_at
    
from employee_stats
order by total_calls desc
