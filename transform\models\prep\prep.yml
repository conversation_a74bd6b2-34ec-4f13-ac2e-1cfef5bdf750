version: 2

models:
  - name: prep.activity_unified
    description: "Unified activities"
  - name: prep.billing_contract
    description: "Unified US & CAN contract billing"
  - name: prep.company_stats
    description: "Company statistics"
  - name: prep.india_placement
    description: "A custom placement and billing fact for india"
  - name: prep.india_split_detail
    description: "India split detail info with overall split info like percent of split accounted by india"
  - name: prep.currency_rates_dedup_balance
    description: "Currency rates deduplication and balancing of both sides (origin vs target)"
  - name: prep.sales_lead_assignment_info
    description: "Sales lead assignment info (first/last)"
  - name: prep.sales_lead_attribution
    description: "Sales lead attribution - exploded attributions to contact, opportunity, job_order and placement"
  - name: prep.placement
    description: "Placement prep after ingest"
  - name: prep.placement_fact
    description: "Placement fact prep"
  - name: prep.production_amount
    description: "Production amount fact prep"
  - name: prep.ringcentral_employee_mapping
    description: "Employee mapping and activity statistics from RingCentral call data"
    columns:
      - name: employee_upn
        description: "Employee User Principal Name"
        tests:
          - unique:
              name: prep_ringcentral_employee_mapping_upn_unique
          - not_null:
              name: prep_ringcentral_employee_mapping_upn_not_null
      - name: total_calls
        description: "Total number of calls (inbound + outbound)"
        tests:
          - not_null:
              name: prep_ringcentral_employee_mapping_total_calls_not_null
  # - name: prep.employee
  #   description: "Prep employee which is a super-set merge of Edge_Users(CMDBUSER) and UNV_Employee, established EMPLOYEE_ID as unique PK"