# RingCentral Call Log Implementation - Planning Document

## Project Overview
**Jira Ticket Goal**: Implement RingCentral phone call data pipeline from raw source through to Power BI reporting, focusing on employee call activity analysis and outcomes.

**Key Business Questions**:
- Which days and times are most likely to have calls picked up?
- What constitutes a meaningful interaction (duration thresholds)?
- How to filter out non-productive calls (e.g., voicemail checks)?
- What's the overall proportion of candidate vs client vs other calls?
- Employee call activity profiling and performance metrics

## Current Data Landscape

### Source Data Location
- **Current Table**: `dlukic_dev.ringcentral.call_log`
- **Records**: 115,295 calls over ~3 months (March-June 2025)
- **Key Insight**: This is a sample extraction in a dev schema, not the production source

### Data Profile Summary
- **Time Range**: March 13, 2025 - June 12, 2025 (~3 months)
- **Employees**: 188 unique FROM employees, 228 unique TO employees
- **Call Volume**: ~1,300 calls per day average
- **Direction Split**: 82% Outbound, 18% Inbound
- **Success Rate**: 71.8% calls connected, 7.9% accepted
- **Average Duration**: 134 seconds (~2.2 minutes)
- **Duration Range**: 0 to 7,246 seconds (2+ hours max)

### Key Data Elements
- **Primary Keys**: `SESSION_ID` (unique per call), `CALL_RECORD_ID`
- **Time**: `START_TIME` (UTC), needs local time conversion
- **Duration**: In seconds
- **Participants**: Employee UPNs and Entra IDs, phone numbers
- **Outcomes**: 17 different result categories
- **Rich Metadata**: JSON payload for future analysis

## Architecture Strategy

### Data Flow Design
```
Source (Future Production) → Ingest → ODS → UDW Fact/Dim → Power BI
```

**Note**: Client mentioned no prep layer needed - direct ODS to UDW transformation.

### Key Implementation Decisions

#### 1. Source Configuration
- **Challenge**: Sample data in different schema than typical `JUDGE_SOURCE`
- **Solution**: Create new source configuration pointing to production location
- **Action**: Need to determine actual production source location

#### 2. Primary Key Strategy
- **Recommendation**: Use `SESSION_ID` as primary key (99.98% unique)
- **Backup**: `CALL_RECORD_ID` as alternative unique identifier
- **Upsert Strategy**: Weekly lookback with ID-based upserts

#### 3. Time Zone Handling
- **Issue**: `START_TIME` is in UTC
- **Solution**: Convert to local business time zones for reporting
- **Consider**: Employee location mapping for multi-timezone analysis

#### 4. Duration Thresholds
- **Business Need**: Define "meaningful interaction" duration
- **Current Data**: 0-7246 seconds range, 134s average
- **Recommendation**: Implement configurable threshold (suggest 30s minimum)
- **Implementation**: Add calculated fields for interaction classification

#### 5. Call Classification Logic
- **Self-Calling Detection**: Match FROM/TO employee fields
- **Candidate Calls**: TBD - requires external candidate data mapping
- **Client Calls**: TBD - requires contact/client data mapping
- **Internal Calls**: Both FROM and TO have employee UPNs

## Dimensional Model Design

### Fact Table: `fact_call_activity`
**Grain**: One record per phone call
**Key Measures**:
- Duration (seconds)
- Call count
- Connected flag
- Meaningful interaction flag

### Dimension Tables

#### `dim_call_outcome`
- Result categories (17 types)
- Success/failure classification
- Meaningful interaction classification

#### `dim_employee` (existing or enhanced)
- Employee UPN mapping
- Entra ID linkage
- Department/role information

#### `dim_date_time` (existing)
- Standard date/time dimensions
- Business hours classification
- Day of week analysis

#### `dim_phone_number` (new)
- External phone numbers
- Geographic location data (from JSON)
- Classification (candidate/client/other)

## Success Metrics & KPIs

### Primary Metrics
1. **Call Volume**: Inbound/Outbound counts by employee, day, time
2. **Connection Rate**: % of calls that result in "Call connected" or "Accepted"
3. **Meaningful Interaction Rate**: % of calls exceeding duration threshold
4. **Response Time Analysis**: Time-of-day and day-of-week patterns
5. **Employee Activity Profiling**: Calls per employee per day/week

### Advanced Analytics
1. **Call Outcome Prediction**: Patterns in successful call times
2. **Duration Analysis**: Correlation between time-of-day and call length
3. **Geographic Analysis**: Success rates by location (from JSON data)
4. **Trend Analysis**: Weekly/monthly call activity trends

## Technical Implementation Phases

### Phase 1: Foundation (Week 1)
1. Set up new source configuration
2. Create basic ingest pipeline
3. Build ODS model with data quality checks
4. Implement primary UDW fact table

### Phase 2: Enhancement (Week 2)
1. Add dimensional tables
2. Implement duration threshold logic
3. Create time zone conversion
4. Build self-call detection

### Phase 3: Analytics (Week 3)
1. Power BI data model
2. Basic reports and dashboards
3. Employee activity metrics
4. Time-based analysis views

### Phase 4: Advanced Features (Week 4)
1. JSON payload analysis integration
2. External data mapping (candidates/clients)
3. Advanced analytics and insights
4. User acceptance testing

## Risk Mitigation

### Technical Risks
- **Source Location Change**: Confirm production source early
- **Data Volume**: 115K records manageable, but plan for scale
- **JSON Complexity**: Rich payload data may need structured parsing
- **Time Zone Complexity**: Multiple employee locations

### Business Risks
- **Threshold Definition**: Get business sign-off on meaningful interaction criteria
- **Privacy Concerns**: Ensure employee call monitoring compliance
- **Data Accuracy**: Validate against existing Edge reporting system

## Next Steps

### Immediate Actions (Today)
1. [ ] Confirm production source location with client
2. [ ] Create source configuration
3. [ ] Set up basic ingest pipeline
4. [ ] Start ODS model development

### Week 1 Deliverables
- [ ] Working ingest from production source
- [ ] ODS model with basic transformations
- [ ] Initial UDW fact table
- [ ] Data quality validation

### Questions for Client
1. What is the actual production source location?
2. What duration threshold defines a "meaningful interaction"?
3. Do we have candidate/client mapping data available?
4. What timezone should be used for reporting (employee-based or company standard)?
5. Are there specific employees or call types to exclude from analysis?